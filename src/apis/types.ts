// 招聘边框详情
export type FrameDetail = {
  id: number
  url: string
}
// 活动详情
export type EventItem = {
  id: number
  name: string
  theme_id: number
  layout_direction: string
  cover_image_url: string
  available_points: number
  ui_style: string
  desc: string
  created_at: number
  user_id: number
  user_name: string
  theme_name: string
  event_frames: FrameDetail[]
  enable_download: boolean
  enable_evaluation: boolean
  enable_mail: boolean
  enable_print: boolean
  enable_sms: boolean
  generations_per_model: number
  models_per_generation: number
  start_time: number
  stop_time: number
  event_themes: ThemeDetail[]
}
// 虚拟设备信息
export type VirtualInfo = {
  id: number
  uid: string
  user_id: number
  company_id: number
  event_id: number
  device_id: number
  device_uid: string
  device_token: string
  expire_ts: number
  status: number
}
// 主题详情
export type ThemeDetail = {
  cover_image: string
  id: number
  name: string
  template_count: number
  type: 0 | 1 // 0: 图片 1: 视频
  price: number
  row?: number
  female_model_count?: number
  male_model_count?: number
}

export type ThemeList = {
  data: {
    data: ThemeDetail[]
  }
}
