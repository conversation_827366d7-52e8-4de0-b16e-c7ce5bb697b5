import classnames from 'classnames'
import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'
import styles from './MazeSingleTemplate.module.css'
import { useSetAtom, useAtomValue } from 'jotai'
import { isShowThemeDetailModalAtom, screenOrientationAtom } from '@/stores'
import { ThemeDetail } from '@/apis/types'
import { MirrorSexEnum } from '@/graphqls/types'
import { isIphone } from '@/utils'

/** 单个模版 */
function MazeSingleTemplate({
  item,
  active = false,
  onSelect,
  isMultiple = false,
  className,
  activeGender,
}: {
  item: ThemeDetail
  active: boolean
  isMultiple?: boolean
  className?: string
  activeGender?: string
  onSelect: () => void
}) {
  const setThemeDetailModalOpen = useSetAtom(isShowThemeDetailModalAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)

  return (
    <div
      className={classnames(
        className,
        styles.template,
        isMultiple && styles.multiple,
        {
          [styles.active]: active,
        },
        ' relative'
      )}
      onClick={onSelect}
    >
      {item.female_model_count || item.male_model_count ? (
        <div
          onClick={() => setThemeDetailModalOpen(true)}
          className={classNames(
            'absolute cursor-pointer bg-[#EDF0F4] leading-none text-[2.25rem] left-8 bottom-16 rounded-xl px-3 py-2 flex items-center justify-center gap-2',
            screenOrientation.isLandScape ? 'bottom-12' : ''
          )}
        >
          <span className="">x</span>
          <span>
            {activeGender === MirrorSexEnum.FEMALE
              ? item.female_model_count
              : item.male_model_count}
          </span>
        </div>
      ) : (
        <div className="absolute"></div>
      )}
      <MyImage
        src={item.cover_image!}
        tag="v800"
        className={classNames('rounded-[3rem] ipad:rounded-[2rem]', [
          screenOrientation.isPortrait
            ? isIphone()
              ? 'h-[55dvh]'
              : 'h-[62dvh]'
            : 'h-[780px]',
        ])}
        imgClassName="object-cover"
      />
    </div>
  )
}

export default MazeSingleTemplate
